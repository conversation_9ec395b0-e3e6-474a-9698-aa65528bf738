<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
  <title>iOS 18 风格天气卡片</title>
  <style>
    :root {
      --bg: radial-gradient(1200px 800px at 20% 10%, #1f1f3b 0%, #0e0e1a 55%, #0a0a14 100%);
      --glass: rgba(255, 255, 255, 0.08);
      --glass-strong: rgba(255, 255, 255, 0.12);
      --stroke: rgba(255, 255, 255, 0.22);
      --shadow: 0 8px 30px rgba(0,0,0,0.35), inset 0 0 0 1px rgba(255,255,255,0.08);
      --radius-xl: 28px;
      --radius-2xl: 32px;
      --text: #e9ebf1;
      --muted: #b8bed0;
      --accent: #4da3ff;
      --accent-2: #9b8cff;
      --ok: #ffd166;
      --warn: #ff7a7a;
      --good: #5cf2b5;
    }

    * { box-sizing: border-box; }
    html, body { height: 100%; }
    body {
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: var(--text);
      background: var(--bg), #0b0b14;
      background-attachment: fixed;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    .page {
      padding: env(safe-area-inset-top) 20px 24px 20px;
      display: flex;
      flex-direction: column;
      gap: 18px;
      min-height: 100dvh;
    }

    header.topbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 12px;
    }
    .brand {
      display: flex; align-items: baseline; gap: 10px;
    }
    .brand h1 {
      margin: 0; font-size: 22px; letter-spacing: 0.2px; font-weight: 700;
    }
    .brand .subtitle { color: var(--muted); font-size: 13px; }

    .actions { display: flex; gap: 10px; align-items: center; }
    .chip {
      border-radius: 999px; padding: 8px 12px; font-size: 13px; color: var(--text);
      background: var(--glass);
      -webkit-backdrop-filter: saturate(160%) blur(14px);
      backdrop-filter: saturate(160%) blur(14px);
      border: 1px solid rgba(255,255,255,0.12);
      box-shadow: var(--shadow);
      cursor: pointer; user-select: none;
      transition: transform .2s ease, background .2s ease;
    }
    .chip:hover { transform: translateY(-1px); background: var(--glass-strong); }

    .wrap {
      position: relative;
      display: grid;
      grid-template-columns: 48px 1fr 48px;
      gap: 12px;
      align-items: center;
    }
    .cards {
      display: flex; gap: 16px; overflow-x: auto; scroll-snap-type: x mandatory; padding: 6px 6px 14px 6px;
      scrollbar-width: none; -ms-overflow-style: none;
    }
    .cards::-webkit-scrollbar { display: none; }

    .nav {
      height: 48px; width: 48px; border-radius: 14px; border: 1px solid rgba(255,255,255,.12);
      background: linear-gradient(180deg, rgba(255,255,255,.12), rgba(255,255,255,.06));
      -webkit-backdrop-filter: blur(10px); backdrop-filter: blur(10px);
      display: grid; place-items: center; color: var(--text); cursor: pointer;
      box-shadow: var(--shadow);
      transition: transform .2s ease, background .2s ease, opacity .2s ease;
      opacity: .9;
    }
    .nav:hover { transform: translateY(-1px); background: rgba(255,255,255,.16); }
    .nav:active { transform: translateY(0); }

    .card {
      position: relative; scroll-snap-align: center; min-width: 340px; width: 360px; aspect-ratio: 16/9;
      border-radius: var(--radius-2xl);
      overflow: hidden;
      background: linear-gradient(180deg, rgba(255,255,255,0.12), rgba(255,255,255,0.06));
      border: 1px solid var(--stroke);
      -webkit-backdrop-filter: blur(16px) saturate(140%);
      backdrop-filter: blur(16px) saturate(140%);
      box-shadow: var(--shadow);
      transition: transform .3s cubic-bezier(.2,.8,.2,1), box-shadow .3s ease, border-color .3s ease;
      transform-style: preserve-3d;
    }
    .card:hover { box-shadow: 0 12px 40px rgba(0,0,0,.5), inset 0 0 0 1px rgba(255,255,255,.10); }

    .card .bg-anim { position: absolute; inset: 0; z-index: 0; }

    /* Weather themed gradients */
    .card.sunny .bg-anim {
      background: radial-gradient(800px 400px at 120% -10%, rgba(255,232,128,.35), rgba(255,184,0,.12) 40%, transparent 60%),
                  radial-gradient(500px 280px at -10% 110%, rgba(77,163,255,.25), transparent 60%);
    }
    .card.windy .bg-anim {
      background: radial-gradient(900px 400px at -20% -10%, rgba(155,140,255,.28), transparent 60%),
                  radial-gradient(600px 300px at 120% 90%, rgba(77,163,255,.16), transparent 60%);
    }
    .card.rain .bg-anim {
      background: radial-gradient(700px 320px at 110% -10%, rgba(77,163,255,.38), transparent 60%),
                  radial-gradient(600px 320px at -10% 110%, rgba(0,114,255,.16), transparent 60%);
    }
    .card.blizzard .bg-anim {
      background: radial-gradient(800px 340px at -10% -10%, rgba(190,220,255,.28), transparent 60%),
                  radial-gradient(600px 300px at 110% 110%, rgba(160,180,255,.20), transparent 60%);
    }

    .content { position: relative; z-index: 2; height: 100%; padding: 16px 18px; display: grid; grid-template-rows: auto 1fr auto; }
    .top { display: flex; align-items: baseline; gap: 10px; }
    .title { font-weight: 800; letter-spacing: .2px; }
    .tag { font-size: 12px; color: var(--muted); padding: 4px 8px; border-radius: 999px; background: rgba(255,255,255,.08); border: 1px solid rgba(255,255,255,.14); }

    .center { position: relative; display: grid; grid-template-columns: 1fr auto; align-items: center; }
    .temp { font-size: 44px; font-weight: 800; letter-spacing: .5px; text-shadow: 0 6px 20px rgba(0,0,0,.35); }
    .temp .unit { font-size: 18px; color: var(--muted); margin-left: 4px; font-weight: 600; }
    .meta { display: flex; gap: 14px; color: var(--muted); font-size: 13px; }

    /* Card icons */
    .icon { width: 120px; height: 120px; position: relative; }

    /* Sun */
    .sun {
      position: absolute; left: 8px; top: 6px; width: 96px; height: 96px; border-radius: 50%;
      background: radial-gradient(circle at 30% 30%, #fff7c5 0%, #ffe37a 35%, #ffca3a 60%, #ffb703 100%);
      box-shadow: 0 0 40px 10px rgba(255,193,7,.35);
    }
    .rays { position: absolute; inset: -8px; border-radius: 50%; border: 8px solid rgba(255,206,86,.6); filter: blur(1px); animation: spin 14s linear infinite; }
    @keyframes spin { to { transform: rotate(360deg); } }

    /* Wind */
    .wind { position: absolute; inset: 20px 0 0 0; }
    .gust { position: absolute; left: 0; right: 0; height: 6px; border-radius: 999px; background: linear-gradient(90deg, transparent, rgba(255,255,255,.65), transparent); filter: blur(1px); opacity: .9; }
    .gust:nth-child(1) { top: 8px; animation: gust 3.6s ease-in-out infinite; }
    .gust:nth-child(2) { top: 34px; animation: gust 3s .4s ease-in-out infinite; }
    .gust:nth-child(3) { top: 60px; animation: gust 4.2s .8s ease-in-out infinite; }
    @keyframes gust { 0% { transform: translateX(-10%); opacity: .1;} 30%{opacity: .9;} 50% { transform: translateX(30%);} 100% { transform: translateX(110%); opacity: .2;} }

    /* Cloud (shared) */
    .cloud { position: absolute; left: 12px; top: 22px; width: 86px; height: 50px; background: linear-gradient(180deg, #eef5ff, #d6e7ff); border-radius: 26px; box-shadow: inset 0 -6px 12px rgba(0,0,0,.06); }
    .cloud::before, .cloud::after { content: ""; position: absolute; background: inherit; border-radius: 50%; }
    .cloud::before { width: 44px; height: 44px; left: -10px; top: -10px; }
    .cloud::after  { width: 54px; height: 54px; left: 40px; top: -18px; }

    /* Rain */
    .rainfield { position: absolute; inset: 0; overflow: hidden; }
    .drop { position: absolute; top: -10%; width: 2px; height: 14px; background: linear-gradient(180deg, rgba(255,255,255,.0), rgba(170,210,255,.9)); border-radius: 1px; filter: blur(.2px); animation: fall linear infinite; }
    @keyframes fall { to { transform: translateY(160px); } }

    /* Blizzard snow */
    .snowfield { position: absolute; inset: 0; overflow: hidden; pointer-events: none; }
    .flake { position: absolute; top: -8px; width: 6px; height: 6px; border-radius: 50%; background: #fff; box-shadow: 0 0 8px rgba(255,255,255,.8); opacity: .9; animation: snow linear infinite; }
    @keyframes snow { to { transform: translate3d(var(--dx, 0px), 160px, 0) rotate(360deg); opacity: .85; } }

    .bottom { display: flex; align-items: center; justify-content: space-between; }
    .metrics { display: flex; gap: 14px; font-size: 13px; color: var(--muted); }
    .metric { display: flex; gap: 6px; align-items: center; }
    .dot { width: 8px; height: 8px; border-radius: 50%; display: inline-block; }

    /* Details panel */
    .details { position: absolute; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,.25); border-top: 1px solid rgba(255,255,255,.14); -webkit-backdrop-filter: blur(10px); backdrop-filter: blur(10px); padding: 14px 16px; transform: translateY(100%); opacity: 0; transition: transform .35s cubic-bezier(.2,.8,.2,1), opacity .35s ease; }
    .card.expanded .details { transform: translateY(0); opacity: 1; }
    .forecast { display: grid; grid-template-columns: repeat(4, 1fr); gap: 8px; font-size: 12px; color: var(--muted); }
    .forecast .cell { background: rgba(255,255,255,.06); border: 1px solid rgba(255,255,255,.12); border-radius: 10px; padding: 8px; text-align: center; color: var(--text); }

    /* Scroll indicator */
    .indicator { height: 4px; border-radius: 999px; background: rgba(255,255,255,.08); overflow: hidden; }
    .indicator .bar { height: 100%; width: 12%; background: linear-gradient(90deg, var(--accent), var(--accent-2)); border-radius: inherit; box-shadow: 0 0 16px var(--accent); transition: width .25s ease; }

    /* Responsiveness */
    @media (max-width: 420px) {
      .card { min-width: 280px; width: 300px; }
      .icon { width: 96px; height: 96px; }
      .temp { font-size: 38px; }
    }
  </style>
</head>
<body>
  <div class="page">
    <header class="topbar">
      <div class="brand">
        <h1>天气</h1>
        <span class="subtitle">iOS 18 风格卡片</span>
      </div>
      <div class="actions">
        <button id="unitToggle" class="chip" aria-label="切换摄氏/华氏">℃ / ℉</button>
        <button id="expandAll" class="chip" aria-label="展开/收起详情">展开详情</button>
      </div>
    </header>

    <div class="wrap">
      <button class="nav" id="prevBtn" title="上一组" aria-label="上一组">
        <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M15 18l-6-6 6-6"/></svg>
      </button>

      <div class="cards" id="cards" aria-label="天气卡片">
        <!-- Sunny -->
        <article class="card sunny" data-celsius="28" data-wind="6" data-humidity="40" role="group" aria-label="晴天卡片">
          <div class="bg-anim"></div>
          <div class="content">
            <div class="top">
              <div class="title">晴天</div>
              <div class="tag">深圳 · 今日</div>
            </div>
            <div class="center">
              <div class="icon">
                <div class="sun"></div>
                <div class="rays"></div>
              </div>
              <div class="temp"><span class="value">28</span><span class="unit">°C</span></div>
            </div>
            <div class="bottom">
              <div class="metrics">
                <div class="metric"><span class="dot" style="background: var(--ok)"></span>体感 30°</div>
                <div class="metric"><span class="dot" style="background: var(--good)"></span>湿度 <span class="hum">40</span>%</div>
                <div class="metric"><span class="dot" style="background: var(--accent)"></span>风速 <span class="wind">6</span> m/s</div>
              </div>
              <button class="chip toggle">详情</button>
            </div>
          </div>
          <div class="details" aria-hidden="true">
            <div class="forecast">
              <div class="cell">12时 31°</div>
              <div class="cell">15时 33°</div>
              <div class="cell">18时 30°</div>
              <div class="cell">21时 27°</div>
            </div>
          </div>
        </article>

        <!-- Windy -->
        <article class="card windy" data-celsius="19" data-wind="14" data-humidity="55" role="group" aria-label="大风卡片">
          <div class="bg-anim"></div>
          <div class="content">
            <div class="top">
              <div class="title">大风</div>
              <div class="tag">青岛 · 今日</div>
            </div>
            <div class="center">
              <div class="icon">
                <div class="wind">
                  <div class="gust"></div>
                  <div class="gust"></div>
                  <div class="gust"></div>
                </div>
                <div class="cloud" style="left: 34px; top: 38px; transform: scale(.9)"></div>
              </div>
              <div class="temp"><span class="value">19</span><span class="unit">°C</span></div>
            </div>
            <div class="bottom">
              <div class="metrics">
                <div class="metric"><span class="dot" style="background: var(--warn)"></span>阵风 18 m/s</div>
                <div class="metric"><span class="dot" style="background: var(--good)"></span>湿度 <span class="hum">55</span>%</div>
                <div class="metric"><span class="dot" style="background: var(--accent)"></span>风速 <span class="wind">14</span> m/s</div>
              </div>
              <button class="chip toggle">详情</button>
            </div>
          </div>
          <div class="details" aria-hidden="true">
            <div class="forecast">
              <div class="cell">12时 阵风</div>
              <div class="cell">15时 多云</div>
              <div class="cell">18时 降温</div>
              <div class="cell">21时 微风</div>
            </div>
          </div>
        </article>

        <!-- Heavy Rain -->
        <article class="card rain" data-celsius="22" data-wind="8" data-humidity="88" role="group" aria-label="暴雨卡片">
          <div class="bg-anim"></div>
          <div class="content">
            <div class="top">
              <div class="title">暴雨</div>
              <div class="tag">广州 · 预警</div>
            </div>
            <div class="center">
              <div class="icon">
                <div class="cloud" style="transform: translateY(6px)"></div>
                <div class="rainfield" data-count="36"></div>
              </div>
              <div class="temp"><span class="value">22</span><span class="unit">°C</span></div>
            </div>
            <div class="bottom">
              <div class="metrics">
                <div class="metric"><span class="dot" style="background: var(--warn)"></span>雷暴概率 70%</div>
                <div class="metric"><span class="dot" style="background: var(--good)"></span>湿度 <span class="hum">88</span>%</div>
                <div class="metric"><span class="dot" style="background: var(--accent)"></span>风速 <span class="wind">8</span> m/s</div>
              </div>
              <button class="chip toggle">详情</button>
            </div>
          </div>
          <div class="details" aria-hidden="true">
            <div class="forecast">
              <div class="cell">12时 大雨</div>
              <div class="cell">15时 暴雨</div>
              <div class="cell">18时 中雨</div>
              <div class="cell">21时 阵雨</div>
            </div>
          </div>
        </article>

        <!-- Blizzard -->
        <article class="card blizzard" data-celsius="-7" data-wind="10" data-humidity="68" role="group" aria-label="暴雪卡片">
          <div class="bg-anim"></div>
          <div class="content">
            <div class="top">
              <div class="title">暴雪</div>
              <div class="tag">哈尔滨 · 寒潮</div>
            </div>
            <div class="center">
              <div class="icon">
                <div class="cloud" style="left: 18px; top: 28px"></div>
                <div class="snowfield" data-count="26"></div>
              </div>
              <div class="temp"><span class="value">-7</span><span class="unit">°C</span></div>
            </div>
            <div class="bottom">
              <div class="metrics">
                <div class="metric"><span class="dot" style="background: var(--accent-2)"></span>体感 -12°</div>
                <div class="metric"><span class="dot" style="background: var(--good)"></span>湿度 <span class="hum">68</span>%</div>
                <div class="metric"><span class="dot" style="background: var(--accent)"></span>风速 <span class="wind">10</span> m/s</div>
              </div>
              <button class="chip toggle">详情</button>
            </div>
          </div>
          <div class="details" aria-hidden="true">
            <div class="forecast">
              <div class="cell">12时 小雪</div>
              <div class="cell">15时 中雪</div>
              <div class="cell">18时 大雪</div>
              <div class="cell">21时 阵雪</div>
            </div>
          </div>
        </article>
      </div>

      <button class="nav" id="nextBtn" title="下一组" aria-label="下一组">
        <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 18l6-6-6-6"/></svg>
      </button>
    </div>

    <div class="indicator" aria-hidden="true"><div class="bar" id="scrollBar"></div></div>
  </div>

  <script>
    const cards = document.getElementById('cards');
    const unitToggle = document.getElementById('unitToggle');
    const expandAllBtn = document.getElementById('expandAll');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const bar = document.getElementById('scrollBar');

    let useCelsius = true;

    // Populate animated particles (rain/snow)
    function seedParticles() {
      document.querySelectorAll('.rainfield').forEach(field => {
        const count = parseInt(field.dataset.count || '24', 10);
        for (let i = 0; i < count; i++) {
          const d = document.createElement('div');
          d.className = 'drop';
          const x = Math.random() * 110 - 5; // -5%~105%
          const delay = Math.random() * 1.2;
          const speed = 0.9 + Math.random() * 0.9; // 0.9-1.8s
          d.style.left = x + '%';
          d.style.animationDuration = speed + 's';
          d.style.animationDelay = delay + 's';
          d.style.opacity = (0.5 + Math.random() * 0.5).toFixed(2);
          field.appendChild(d);
        }
      });
      document.querySelectorAll('.snowfield').forEach(field => {
        const count = parseInt(field.dataset.count || '20', 10);
        for (let i = 0; i < count; i++) {
          const f = document.createElement('div');
          f.className = 'flake';
          const x = Math.random() * 100;
          const delay = Math.random() * 1.6;
          const speed = 3 + Math.random() * 2.5; // slower than rain
          const drift = (Math.random() * 40 - 20).toFixed(1);
          f.style.left = x + '%';
          f.style.animationDuration = speed + 's';
          f.style.animationDelay = delay + 's';
          f.style.setProperty('--dx', drift + 'px');
          f.style.opacity = (0.75 + Math.random() * 0.25).toFixed(2);
          field.appendChild(f);
        }
      });
    }

    // Parallax tilt on pointer
    function bindTilt(card) {
      const maxTilt = 10; // deg
      const onMove = (e) => {
        const rect = card.getBoundingClientRect();
        const cx = rect.left + rect.width / 2;
        const cy = rect.top + rect.height / 2;
        const x = (e.clientX - cx) / (rect.width / 2);
        const y = (e.clientY - cy) / (rect.height / 2);
        const rx = (y * -maxTilt).toFixed(2);
        const ry = (x * maxTilt).toFixed(2);
        card.style.transform = `perspective(900px) rotateX(${rx}deg) rotateY(${ry}deg) translateZ(0)`;
      };
      const reset = () => { card.style.transform = ''; };
      card.addEventListener('mousemove', onMove);
      card.addEventListener('mouseleave', reset);
    }

    // Expand details toggle
    function bindExpand(card) {
      const btn = card.querySelector('.toggle');
      const details = card.querySelector('.details');
      btn.addEventListener('click', () => {
        const expanded = card.classList.toggle('expanded');
        details.setAttribute('aria-hidden', expanded ? 'false' : 'true');
      });
    }

    // Unit toggle
    function renderUnits() {
      document.querySelectorAll('.card').forEach(card => {
        const baseC = parseFloat(card.dataset.celsius);
        const valueEl = card.querySelector('.temp .value');
        const unitEl = card.querySelector('.temp .unit');
        if (!valueEl) return;
        if (useCelsius) {
          valueEl.textContent = Math.round(baseC);
          unitEl.textContent = '°C';
        } else {
          const f = baseC * 9/5 + 32;
          valueEl.textContent = Math.round(f);
          unitEl.textContent = '°F';
        }
      });
    }

    unitToggle.addEventListener('click', () => { useCelsius = !useCelsius; renderUnits(); });
    expandAllBtn.addEventListener('click', () => {
      const anyCollapsed = Array.from(document.querySelectorAll('.card')).some(c => !c.classList.contains('expanded'));
      document.querySelectorAll('.card').forEach(card => {
        const details = card.querySelector('.details');
        card.classList.toggle('expanded', anyCollapsed);
        details.setAttribute('aria-hidden', anyCollapsed ? 'false' : 'true');
      });
      expandAllBtn.textContent = anyCollapsed ? '收起详情' : '展开详情';
    });

    // Scroll helpers
    function updateIndicator() {
      const max = cards.scrollWidth - cards.clientWidth;
      const p = max > 0 ? cards.scrollLeft / max : 0;
      bar.style.width = (18 + p * 82) + '%'; // 18%-100%
    }
    cards.addEventListener('scroll', updateIndicator);
    prevBtn.addEventListener('click', () => {
      const w = cards.querySelector('.card')?.clientWidth || 360;
      cards.scrollBy({ left: -w - 16, behavior: 'smooth' });
    });
    nextBtn.addEventListener('click', () => {
      const w = cards.querySelector('.card')?.clientWidth || 360;
      cards.scrollBy({ left: w + 16, behavior: 'smooth' });
    });

    // Bind per-card behaviors
    function init() {
      seedParticles();
      document.querySelectorAll('.card').forEach(card => { bindTilt(card); bindExpand(card); });
      renderUnits();
      updateIndicator();
    }

    document.addEventListener('DOMContentLoaded', init);
  </script>
</body>
</html>

