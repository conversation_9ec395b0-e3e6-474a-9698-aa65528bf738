<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
  <title>iOS 18 天气 - 重新设计</title>
  <style>
    :root {
      --bg-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --bg-overlay: radial-gradient(ellipse at top, rgba(255,255,255,0.1) 0%, transparent 70%);
      --glass-bg: rgba(255, 255, 255, 0.1);
      --glass-border: rgba(255, 255, 255, 0.2);
      --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      --text-primary: #ffffff;
      --text-secondary: rgba(255, 255, 255, 0.8);
      --text-muted: rgba(255, 255, 255, 0.6);
      --accent-blue: #007AFF;
      --accent-orange: #FF9500;
      --accent-green: #34C759;
      --accent-red: #FF3B30;
      --radius-lg: 24px;
      --radius-xl: 32px;
      --spacing-xs: 8px;
      --spacing-sm: 12px;
      --spacing-md: 16px;
      --spacing-lg: 24px;
      --spacing-xl: 32px;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    html, body {
      height: 100%;
      overflow-x: hidden;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", Helvetica, Arial, sans-serif;
      background: var(--bg-primary);
      color: var(--text-primary);
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      position: relative;
    }

    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--bg-overlay);
      pointer-events: none;
      z-index: 0;
    }

    .container {
      position: relative;
      z-index: 1;
      min-height: 100vh;
      padding: env(safe-area-inset-top, 44px) var(--spacing-lg) var(--spacing-xl) var(--spacing-lg);
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xl);
    }

    /* Header */
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-md) 0;
    }

    .header-title {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .header-title h1 {
      font-size: 34px;
      font-weight: 700;
      letter-spacing: -0.5px;
      color: var(--text-primary);
    }

    .header-subtitle {
      font-size: 17px;
      font-weight: 400;
      color: var(--text-secondary);
    }

    .header-controls {
      display: flex;
      gap: var(--spacing-sm);
      align-items: center;
    }

    .control-btn {
      background: var(--glass-bg);
      border: 1px solid var(--glass-border);
      border-radius: 20px;
      padding: 10px 16px;
      color: var(--text-primary);
      font-size: 15px;
      font-weight: 500;
      cursor: pointer;
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      user-select: none;
    }

    .control-btn:hover {
      background: rgba(255, 255, 255, 0.15);
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    }

    .control-btn:active {
      transform: translateY(0);
    }

    /* Weather Cards Container */
    .weather-container {
      position: relative;
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);
    }

    .cards-wrapper {
      position: relative;
      overflow: hidden;
      border-radius: var(--radius-xl);
    }

    .cards-scroll {
      display: flex;
      gap: var(--spacing-lg);
      overflow-x: auto;
      scroll-snap-type: x mandatory;
      padding: var(--spacing-md);
      scrollbar-width: none;
      -ms-overflow-style: none;
      scroll-behavior: smooth;
    }

    .cards-scroll::-webkit-scrollbar {
      display: none;
    }

    /* Navigation Buttons */
    .nav-btn {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 44px;
      height: 44px;
      background: var(--glass-bg);
      border: 1px solid var(--glass-border);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      z-index: 10;
      color: var(--text-primary);
    }

    .nav-btn:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-50%) scale(1.1);
    }

    .nav-btn.prev {
      left: var(--spacing-md);
    }

    .nav-btn.next {
      right: var(--spacing-md);
    }

    /* Weather Card */
    .weather-card {
      position: relative;
      min-width: 320px;
      width: 320px;
      height: 400px;
      background: var(--glass-bg);
      border: 1px solid var(--glass-border);
      border-radius: var(--radius-xl);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      box-shadow: var(--glass-shadow);
      scroll-snap-align: center;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      transform-style: preserve-3d;
    }

    .weather-card:hover {
      transform: translateY(-8px) rotateX(5deg);
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
    }

    .weather-card.active {
      transform: scale(1.05);
      box-shadow: 0 25px 80px rgba(0, 0, 0, 0.5);
    }

    /* Weather Background Animations */
    .weather-bg {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      overflow: hidden;
      border-radius: var(--radius-xl);
    }

    .weather-card.sunny .weather-bg {
      background: linear-gradient(135deg,
        rgba(255, 193, 7, 0.3) 0%,
        rgba(255, 152, 0, 0.2) 50%,
        rgba(255, 87, 34, 0.1) 100%);
    }

    .weather-card.windy .weather-bg {
      background: linear-gradient(135deg,
        rgba(96, 125, 139, 0.3) 0%,
        rgba(69, 90, 100, 0.2) 50%,
        rgba(55, 71, 79, 0.1) 100%);
    }

    .weather-card.rainy .weather-bg {
      background: linear-gradient(135deg,
        rgba(33, 150, 243, 0.3) 0%,
        rgba(21, 101, 192, 0.2) 50%,
        rgba(13, 71, 161, 0.1) 100%);
    }

    .weather-card.snowy .weather-bg {
      background: linear-gradient(135deg,
        rgba(224, 247, 250, 0.3) 0%,
        rgba(178, 235, 242, 0.2) 50%,
        rgba(129, 212, 250, 0.1) 100%);
    }

    /* Card Content */
    .card-content {
      position: relative;
      z-index: 2;
      height: 100%;
      padding: var(--spacing-lg);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--spacing-md);
    }

    .location {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .city-name {
      font-size: 20px;
      font-weight: 600;
      color: var(--text-primary);
    }

    .weather-condition {
      font-size: 15px;
      color: var(--text-secondary);
      font-weight: 400;
    }

    .time-info {
      font-size: 13px;
      color: var(--text-muted);
      text-align: right;
    }

    .card-main {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: var(--spacing-lg) 0;
    }

    .temperature {
      display: flex;
      align-items: baseline;
      gap: 4px;
    }

    .temp-value {
      font-size: 64px;
      font-weight: 300;
      line-height: 1;
      color: var(--text-primary);
    }

    .temp-unit {
      font-size: 24px;
      color: var(--text-secondary);
      font-weight: 400;
    }

    /* Weather Icons */
    .weather-icon {
      width: 120px;
      height: 120px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    /* Sunny Weather Icon */
    .sun-icon {
      width: 80px;
      height: 80px;
      background: radial-gradient(circle at 30% 30%, #FFE082 0%, #FFB74D 50%, #FF9800 100%);
      border-radius: 50%;
      position: relative;
      animation: sunPulse 4s ease-in-out infinite;
      box-shadow: 0 0 30px rgba(255, 193, 7, 0.6);
    }

    .sun-rays {
      position: absolute;
      width: 100px;
      height: 100px;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      animation: sunRotate 20s linear infinite;
    }

    .sun-ray {
      position: absolute;
      width: 3px;
      height: 15px;
      background: linear-gradient(to bottom, rgba(255, 193, 7, 0.8), transparent);
      border-radius: 2px;
      top: 0;
      left: 50%;
      transform-origin: 50% 50px;
    }

    .sun-ray:nth-child(1) { transform: translateX(-50%) rotate(0deg); }
    .sun-ray:nth-child(2) { transform: translateX(-50%) rotate(45deg); }
    .sun-ray:nth-child(3) { transform: translateX(-50%) rotate(90deg); }
    .sun-ray:nth-child(4) { transform: translateX(-50%) rotate(135deg); }
    .sun-ray:nth-child(5) { transform: translateX(-50%) rotate(180deg); }
    .sun-ray:nth-child(6) { transform: translateX(-50%) rotate(225deg); }
    .sun-ray:nth-child(7) { transform: translateX(-50%) rotate(270deg); }
    .sun-ray:nth-child(8) { transform: translateX(-50%) rotate(315deg); }

    @keyframes sunPulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }

    @keyframes sunRotate {
      from { transform: translate(-50%, -50%) rotate(0deg); }
      to { transform: translate(-50%, -50%) rotate(360deg); }
    }

    /* Wind Icon */
    .wind-icon {
      width: 100px;
      height: 80px;
      position: relative;
    }

    .wind-line {
      position: absolute;
      height: 4px;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
      border-radius: 2px;
      animation: windFlow 3s ease-in-out infinite;
    }

    .wind-line:nth-child(1) {
      top: 20px;
      width: 80px;
      animation-delay: 0s;
    }

    .wind-line:nth-child(2) {
      top: 40px;
      width: 60px;
      left: 20px;
      animation-delay: 0.5s;
    }

    .wind-line:nth-child(3) {
      top: 60px;
      width: 70px;
      left: 10px;
      animation-delay: 1s;
    }

    @keyframes windFlow {
      0%, 100% { opacity: 0.3; transform: translateX(-10px); }
      50% { opacity: 1; transform: translateX(10px); }
    }

    /* Rain Icon */
    .rain-icon {
      width: 100px;
      height: 80px;
      position: relative;
    }

    .cloud {
      width: 60px;
      height: 30px;
      background: linear-gradient(to bottom, #E3F2FD, #BBDEFB);
      border-radius: 30px;
      position: absolute;
      top: 10px;
      left: 20px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .cloud::before,
    .cloud::after {
      content: '';
      position: absolute;
      background: inherit;
      border-radius: 50%;
    }

    .cloud::before {
      width: 25px;
      height: 25px;
      top: -12px;
      left: 10px;
    }

    .cloud::after {
      width: 35px;
      height: 35px;
      top: -15px;
      right: 5px;
    }

    .rain-drops {
      position: absolute;
      top: 40px;
      left: 25px;
      width: 50px;
      height: 40px;
    }

    .rain-drop {
      position: absolute;
      width: 2px;
      height: 12px;
      background: linear-gradient(to bottom, rgba(33, 150, 243, 0.8), rgba(33, 150, 243, 0.3));
      border-radius: 0 0 2px 2px;
      animation: rainFall 1.5s linear infinite;
    }

    .rain-drop:nth-child(1) { left: 10px; animation-delay: 0s; }
    .rain-drop:nth-child(2) { left: 20px; animation-delay: 0.3s; }
    .rain-drop:nth-child(3) { left: 30px; animation-delay: 0.6s; }
    .rain-drop:nth-child(4) { left: 40px; animation-delay: 0.9s; }

    @keyframes rainFall {
      0% { transform: translateY(-10px); opacity: 0; }
      10% { opacity: 1; }
      100% { transform: translateY(30px); opacity: 0; }
    }

    /* Snow Icon */
    .snow-icon {
      width: 100px;
      height: 80px;
      position: relative;
    }

    .snow-cloud {
      width: 60px;
      height: 30px;
      background: linear-gradient(to bottom, #F5F5F5, #E0E0E0);
      border-radius: 30px;
      position: absolute;
      top: 10px;
      left: 20px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .snow-cloud::before,
    .snow-cloud::after {
      content: '';
      position: absolute;
      background: inherit;
      border-radius: 50%;
    }

    .snow-cloud::before {
      width: 25px;
      height: 25px;
      top: -12px;
      left: 10px;
    }

    .snow-cloud::after {
      width: 35px;
      height: 35px;
      top: -15px;
      right: 5px;
    }

    .snowflakes {
      position: absolute;
      top: 40px;
      left: 20px;
      width: 60px;
      height: 40px;
    }

    .snowflake {
      position: absolute;
      color: white;
      font-size: 12px;
      animation: snowFall 3s linear infinite;
      user-select: none;
    }

    .snowflake:nth-child(1) { left: 10px; animation-delay: 0s; }
    .snowflake:nth-child(2) { left: 25px; animation-delay: 1s; }
    .snowflake:nth-child(3) { left: 40px; animation-delay: 2s; }

    @keyframes snowFall {
      0% { transform: translateY(-10px) rotate(0deg); opacity: 0; }
      10% { opacity: 1; }
      100% { transform: translateY(40px) rotate(360deg); opacity: 0; }
    }
    /* Card Footer */
    .card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .weather-details {
      display: flex;
      gap: var(--spacing-md);
    }

    .detail-item {
      display: flex;
      flex-direction: column;
      gap: 2px;
      align-items: center;
    }

    .detail-label {
      font-size: 12px;
      color: var(--text-muted);
      font-weight: 400;
    }

    .detail-value {
      font-size: 15px;
      color: var(--text-primary);
      font-weight: 500;
    }

    .expand-btn {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 12px;
      padding: 8px 12px;
      color: var(--text-primary);
      font-size: 13px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .expand-btn:hover {
      background: rgba(255, 255, 255, 0.2);
    }

    /* Scroll Indicator */
    .scroll-indicator {
      display: flex;
      justify-content: center;
      gap: 8px;
      margin-top: var(--spacing-lg);
    }

    .indicator-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .indicator-dot.active {
      background: var(--text-primary);
      transform: scale(1.2);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .container {
        padding: env(safe-area-inset-top, 20px) var(--spacing-md) var(--spacing-lg) var(--spacing-md);
      }

      .weather-card {
        min-width: 280px;
        width: 280px;
        height: 360px;
      }

      .temp-value {
        font-size: 48px;
      }

      .weather-icon {
        width: 100px;
        height: 100px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <header class="header">
      <div class="header-title">
        <h1>天气</h1>
        <div class="header-subtitle">今天 · 北京时间</div>
      </div>
      <div class="header-controls">
        <button class="control-btn" id="unitToggle">°C / °F</button>
        <button class="control-btn" id="locationBtn">📍</button>
      </div>
    </header>

    <div class="weather-container">
      <div class="cards-wrapper">
        <button class="nav-btn prev" id="prevBtn">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M15 18l-6-6 6-6"/>
          </svg>
        </button>

        <div class="cards-scroll" id="cardsContainer">
          <!-- Sunny Weather Card -->
          <div class="weather-card sunny" data-temp="28" data-condition="晴天">
            <div class="weather-bg"></div>
            <div class="card-content">
              <div class="card-header">
                <div class="location">
                  <div class="city-name">深圳</div>
                  <div class="weather-condition">晴天</div>
                </div>
                <div class="time-info">
                  <div>今天</div>
                  <div>14:30</div>
                </div>
              </div>

              <div class="card-main">
                <div class="temperature">
                  <span class="temp-value">28</span>
                  <span class="temp-unit">°</span>
                </div>
                <div class="weather-icon">
                  <div class="sun-icon"></div>
                  <div class="sun-rays">
                    <div class="sun-ray"></div>
                    <div class="sun-ray"></div>
                    <div class="sun-ray"></div>
                    <div class="sun-ray"></div>
                    <div class="sun-ray"></div>
                    <div class="sun-ray"></div>
                    <div class="sun-ray"></div>
                    <div class="sun-ray"></div>
                  </div>
                </div>
              </div>

              <div class="card-footer">
                <div class="weather-details">
                  <div class="detail-item">
                    <div class="detail-label">体感</div>
                    <div class="detail-value">30°</div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">湿度</div>
                    <div class="detail-value">45%</div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">风速</div>
                    <div class="detail-value">6km/h</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Windy Weather Card -->
          <div class="weather-card windy" data-temp="19" data-condition="大风">
            <div class="weather-bg"></div>
            <div class="card-content">
              <div class="card-header">
                <div class="location">
                  <div class="city-name">青岛</div>
                  <div class="weather-condition">大风</div>
                </div>
                <div class="time-info">
                  <div>今天</div>
                  <div>14:30</div>
                </div>
              </div>

              <div class="card-main">
                <div class="temperature">
                  <span class="temp-value">19</span>
                  <span class="temp-unit">°</span>
                </div>
                <div class="weather-icon">
                  <div class="wind-icon">
                    <div class="wind-line"></div>
                    <div class="wind-line"></div>
                    <div class="wind-line"></div>
                  </div>
                </div>
              </div>

              <div class="card-footer">
                <div class="weather-details">
                  <div class="detail-item">
                    <div class="detail-label">阵风</div>
                    <div class="detail-value">25km/h</div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">湿度</div>
                    <div class="detail-value">55%</div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">风速</div>
                    <div class="detail-value">18km/h</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Rainy Weather Card -->
          <div class="weather-card rainy" data-temp="22" data-condition="暴雨">
            <div class="weather-bg"></div>
            <div class="card-content">
              <div class="card-header">
                <div class="location">
                  <div class="city-name">广州</div>
                  <div class="weather-condition">暴雨</div>
                </div>
                <div class="time-info">
                  <div>今天</div>
                  <div>14:30</div>
                </div>
              </div>

              <div class="card-main">
                <div class="temperature">
                  <span class="temp-value">22</span>
                  <span class="temp-unit">°</span>
                </div>
                <div class="weather-icon">
                  <div class="rain-icon">
                    <div class="cloud"></div>
                    <div class="rain-drops">
                      <div class="rain-drop"></div>
                      <div class="rain-drop"></div>
                      <div class="rain-drop"></div>
                      <div class="rain-drop"></div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="card-footer">
                <div class="weather-details">
                  <div class="detail-item">
                    <div class="detail-label">降雨</div>
                    <div class="detail-value">85%</div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">湿度</div>
                    <div class="detail-value">88%</div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">风速</div>
                    <div class="detail-value">12km/h</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Snowy Weather Card -->
          <div class="weather-card snowy" data-temp="-7" data-condition="暴雪">
            <div class="weather-bg"></div>
            <div class="card-content">
              <div class="card-header">
                <div class="location">
                  <div class="city-name">哈尔滨</div>
                  <div class="weather-condition">暴雪</div>
                </div>
                <div class="time-info">
                  <div>今天</div>
                  <div>14:30</div>
                </div>
              </div>

              <div class="card-main">
                <div class="temperature">
                  <span class="temp-value">-7</span>
                  <span class="temp-unit">°</span>
                </div>
                <div class="weather-icon">
                  <div class="snow-icon">
                    <div class="snow-cloud"></div>
                    <div class="snowflakes">
                      <div class="snowflake">❄</div>
                      <div class="snowflake">❅</div>
                      <div class="snowflake">❆</div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="card-footer">
                <div class="weather-details">
                  <div class="detail-item">
                    <div class="detail-label">体感</div>
                    <div class="detail-value">-12°</div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">湿度</div>
                    <div class="detail-value">68%</div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">风速</div>
                    <div class="detail-value">15km/h</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <button class="nav-btn next" id="nextBtn">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M9 18l6-6-6-6"/>
          </svg>
        </button>
      </div>

      <div class="scroll-indicator">
        <div class="indicator-dot active"></div>
        <div class="indicator-dot"></div>
        <div class="indicator-dot"></div>
        <div class="indicator-dot"></div>
      </div>
    </div>
  </div>

  <script>
    // DOM Elements
    const cardsContainer = document.getElementById('cardsContainer');
    const unitToggle = document.getElementById('unitToggle');
    const locationBtn = document.getElementById('locationBtn');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const indicatorDots = document.querySelectorAll('.indicator-dot');
    const weatherCards = document.querySelectorAll('.weather-card');

    // State
    let currentIndex = 0;
    let isCelsius = true;

    // Weather data
    const weatherData = [
      { city: '深圳', condition: '晴天', temp: 28, humidity: 45, wind: 6, feels: 30 },
      { city: '青岛', condition: '大风', temp: 19, humidity: 55, wind: 18, feels: 16 },
      { city: '广州', condition: '暴雨', temp: 22, humidity: 88, wind: 12, feels: 25 },
      { city: '哈尔滨', condition: '暴雪', temp: -7, humidity: 68, wind: 15, feels: -12 }
    ];

    // Initialize
    function init() {
      updateActiveCard();
      bindEvents();
      startAutoRotation();
    }

    // Event Listeners
    function bindEvents() {
      // Navigation buttons
      prevBtn.addEventListener('click', () => navigateCard(-1));
      nextBtn.addEventListener('click', () => navigateCard(1));

      // Unit toggle
      unitToggle.addEventListener('click', toggleTemperatureUnit);

      // Location button
      locationBtn.addEventListener('click', () => {
        // Simulate location detection
        showNotification('正在获取位置信息...');
      });

      // Card click events
      weatherCards.forEach((card, index) => {
        card.addEventListener('click', () => {
          currentIndex = index;
          updateActiveCard();
          scrollToCard(index);
        });
      });

      // Indicator dots
      indicatorDots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
          currentIndex = index;
          updateActiveCard();
          scrollToCard(index);
        });
      });

      // Touch/scroll events
      cardsContainer.addEventListener('scroll', handleScroll);
    }

    // Navigation
    function navigateCard(direction) {
      currentIndex = (currentIndex + direction + weatherCards.length) % weatherCards.length;
      updateActiveCard();
      scrollToCard(currentIndex);
    }

    function scrollToCard(index) {
      const card = weatherCards[index];
      const container = cardsContainer;
      const cardWidth = card.offsetWidth;
      const gap = 24; // spacing between cards
      const scrollLeft = index * (cardWidth + gap);

      container.scrollTo({
        left: scrollLeft,
        behavior: 'smooth'
      });
    }

    function updateActiveCard() {
      // Update cards
      weatherCards.forEach((card, index) => {
        card.classList.toggle('active', index === currentIndex);
      });

      // Update indicators
      indicatorDots.forEach((dot, index) => {
        dot.classList.toggle('active', index === currentIndex);
      });
    }

    function handleScroll() {
      const container = cardsContainer;
      const cardWidth = weatherCards[0].offsetWidth;
      const gap = 24;
      const scrollLeft = container.scrollLeft;
      const newIndex = Math.round(scrollLeft / (cardWidth + gap));

      if (newIndex !== currentIndex && newIndex >= 0 && newIndex < weatherCards.length) {
        currentIndex = newIndex;
        updateActiveCard();
      }
    }

    // Temperature unit toggle
    function toggleTemperatureUnit() {
      isCelsius = !isCelsius;

      weatherCards.forEach((card, index) => {
        const tempValue = card.querySelector('.temp-value');
        const data = weatherData[index];

        if (isCelsius) {
          tempValue.textContent = data.temp;
          unitToggle.textContent = '°C / °F';
        } else {
          const fahrenheit = Math.round(data.temp * 9/5 + 32);
          tempValue.textContent = fahrenheit;
          unitToggle.textContent = '°F / °C';
        }
      });
    }

    // Auto rotation
    function startAutoRotation() {
      setInterval(() => {
        navigateCard(1);
      }, 8000); // Change card every 8 seconds
    }

    // Utility functions
    function showNotification(message) {
      // Create a simple notification
      const notification = document.createElement('div');
      notification.style.cssText = `
        position: fixed;
        top: 100px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 12px 24px;
        border-radius: 20px;
        font-size: 14px;
        z-index: 1000;
        backdrop-filter: blur(10px);
        animation: slideDown 0.3s ease;
      `;
      notification.textContent = message;
      document.body.appendChild(notification);

      setTimeout(() => {
        notification.style.animation = 'slideUp 0.3s ease forwards';
        setTimeout(() => notification.remove(), 300);
      }, 2000);
    }

    // Add CSS animations for notifications
    const style = document.createElement('style');
    style.textContent = `
      @keyframes slideDown {
        from { transform: translateX(-50%) translateY(-20px); opacity: 0; }
        to { transform: translateX(-50%) translateY(0); opacity: 1; }
      }
      @keyframes slideUp {
        from { transform: translateX(-50%) translateY(0); opacity: 1; }
        to { transform: translateX(-50%) translateY(-20px); opacity: 0; }
      }
    `;
    document.head.appendChild(style);

    // Add 3D tilt effect on mouse move
    weatherCards.forEach(card => {
      card.addEventListener('mousemove', (e) => {
        const rect = card.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;
        const rotateX = (y - centerY) / 10;
        const rotateY = (centerX - x) / 10;

        card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(0)`;
      });

      card.addEventListener('mouseleave', () => {
        card.style.transform = '';
      });
    });

    // Initialize the app
    document.addEventListener('DOMContentLoaded', init);
  </script>
</body>
</html>

